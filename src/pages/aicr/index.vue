<script setup lang="ts">
import type { AicrGroupConfig, FindAicrGroupRequest } from '~/apis/aicr'
import { deleteAicrGroup, getAicrGroupList } from '~/apis/aicr'
import { useUserStore } from '~/stores/user'
import CreateGroupDialog from './components/CreateGroupDialog.vue'
import EditGroupDialog from './components/EditGroupDialog.vue'
import MemberManagement from './components/MemberManagement.vue'
import ProjectManagement from './components/ProjectManagement.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const list = ref<AicrGroupConfig[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(20)

// 搜索条件
const searchForm = reactive({
  name: '',
  type: '',
})

// 对话框状态
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const memberDialogVisible = ref(false)
const projectDialogVisible = ref(false)
const currentGroup = ref<AicrGroupConfig | null>(null)

// 获取群组列表
async function fetchGroupList() {
  loading.value = true
  try {
    const params: FindAicrGroupRequest = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchForm,
    }

    // 过滤空值
    if (!params.name)
      delete params.name
    if (!params.type)
      delete params.type

    const res = await getAicrGroupList(params)
    list.value = res.list
    total.value = res.total
  }
  catch (error) {
    ElMessage.error('获取群组列表失败')
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  page.value = 1
  fetchGroupList()
}

// 重置搜索
function handleReset() {
  searchForm.name = ''
  searchForm.type = ''
  page.value = 1
  fetchGroupList()
}

// 分页变化
function handleSizeChange(val: number) {
  pageSize.value = val
  page.value = 1
  fetchGroupList()
}

function handleCurrentChange(val: number) {
  page.value = val
  fetchGroupList()
}

// 创建群组
function handleCreate() {
  createDialogVisible.value = true
}

// 编辑群组
function handleEdit(row: AicrGroupConfig) {
  currentGroup.value = row
  editDialogVisible.value = true
}

// 删除群组
async function handleDelete(row: AicrGroupConfig) {
  // 检查权限
  if (row.creator !== userStore.userInfo.id) {
    ElMessage.warning('只有创建者可以删除群组')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除群组"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await deleteAicrGroup(row.id)
    ElMessage.success('删除成功')
    fetchGroupList()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 管理成员
function handleManageMembers(row: AicrGroupConfig) {
  if (row.type !== 'normal') {
    ElMessage.warning('只有普通群可以管理成员')
    return
  }
  currentGroup.value = row
  memberDialogVisible.value = true
}

// 管理项目
function handleManageProjects(row: AicrGroupConfig) {
  if (row.type !== 'project') {
    ElMessage.warning('只有项目群可以管理项目关联')
    return
  }
  currentGroup.value = row
  projectDialogVisible.value = true
}

// 格式化群组类型
function formatGroupType(type: string) {
  return type === 'normal' ? '普通群' : '项目群'
}

// 格式化时间
function formatTime(time: string) {
  return new Date(time).toLocaleString()
}

// 对话框关闭后刷新列表
function handleDialogClose() {
  fetchGroupList()
  currentGroup.value = null
}

// 初始化
onMounted(() => {
  fetchGroupList()
})
</script>

<template>
  <div class="aicr-page">
    <h2>AICR群配置管理</h2>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="群组名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入群组名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="群组类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择群组类型"
            clearable
            style="width: 150px"
          >
            <el-option label="普通群" value="normal" />
            <el-option label="项目群" value="project" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-ep-search" />
            搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="i-ep-refresh" />
            重置
          </el-button>
          <el-button type="success" @click="handleCreate">
            <i class="i-ep-plus" />
            新建群组
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="list"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="群组名称" min-width="150" />
        <el-table-column prop="type" label="群组类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.type === 'normal' ? 'primary' : 'success'">
              {{ formatGroupType(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="webhook_url" label="Webhook地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="成员/项目数" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.type === 'normal'">{{ row.members?.length || 0 }}人</span>
            <span v-else>{{ row.projectGroups?.length || 0 }}项目</span>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              v-if="row.type === 'normal'"
              text
              type="primary"
              @click="handleManageMembers(row)"
            >
              管理成员
            </el-button>
            <el-button
              v-if="row.type === 'project'"
              text
              type="primary"
              @click="handleManageProjects(row)"
            >
              管理项目
            </el-button>
            <el-button
              text
              type="danger"
              :disabled="row.creator !== userStore.userInfo.id"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 对话框 -->
    <CreateGroupDialog
      v-model="createDialogVisible"
      @success="handleDialogClose"
    />

    <EditGroupDialog
      v-model="editDialogVisible"
      :group="currentGroup"
      @success="handleDialogClose"
    />

    <MemberManagement
      v-model="memberDialogVisible"
      :group="currentGroup"
      @success="handleDialogClose"
    />

    <ProjectManagement
      v-model="projectDialogVisible"
      :group="currentGroup"
      @success="handleDialogClose"
    />
  </div>
</template>

<style scoped>
.aicr-page {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>

<route lang="yaml">
meta:
  menu:
    name: AICR群配置
    sort: 10
    path: /aicr
    icon: i-ep-chat-dot-round
</route>
