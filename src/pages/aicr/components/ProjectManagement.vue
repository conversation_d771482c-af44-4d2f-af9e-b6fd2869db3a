<script setup lang="ts">
import type { AicrGroupConfig, AicrProjectGroup, ProjectGroupItem } from '~/apis/aicr'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addProjectGroups, getAicrGroupById, removeProjectGroups } from '~/apis/aicr'
import { fetchProjectList } from '~/apis/project'

interface Props {
  modelValue: boolean
  group: AicrGroupConfig | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 数据状态
const loading = ref(false)
const projectGroups = ref<AicrProjectGroup[]>([])
const selectedProjects = ref<string[]>([])

// 添加项目相关
const addDialogVisible = ref(false)
const selectedProjectIds = ref<string[]>([])
const projectOptions = ref<Array<{ value: string, label: string }>>([])
const loadingProjects = ref(false)
const submitting = ref(false)

// 获取项目关联列表
async function fetchProjectGroups() {
  if (!props.group?.id)
    return

  loading.value = true
  try {
    const groupData = await getAicrGroupById(props.group.id)
    projectGroups.value = (groupData.projectGroups || []).sort((a, b) => a.priority - b.priority)
  }
  catch (error) {
    ElMessage.error('获取项目关联列表失败')
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

// 获取项目列表
async function fetchProjects() {
  if (projectOptions.value.length > 0)
    return

  loadingProjects.value = true
  try {
    const res = await fetchProjectList({ page: 1, pageSize: 1000 })
    projectOptions.value = res.list.map(project => ({
      value: project.id,
      label: project.name,
    }))
  }
  catch (error) {
    ElMessage.error('获取项目列表失败')
    console.error(error)
  }
  finally {
    loadingProjects.value = false
  }
}

// 获取可添加的项目（排除已关联项目）
const availableProjects = computed(() => {
  const existingProjectIds = projectGroups.value.map(pg => pg.project_id)
  return projectOptions.value.filter(project => !existingProjectIds.includes(project.value))
})

// 打开添加项目对话框
function handleAddProjects() {
  selectedProjectIds.value = []
  addDialogVisible.value = true
  fetchProjects()
}

// 确认添加项目
async function confirmAddProjects() {
  if (!props.group?.id || selectedProjectIds.value.length === 0) {
    ElMessage.warning('请选择要添加的项目')
    return
  }

  submitting.value = true
  try {
    // 计算新项目的优先级（从当前最大优先级+1开始）
    const maxPriority = Math.max(...projectGroups.value.map(pg => pg.priority), 0)
    const projects: ProjectGroupItem[] = selectedProjectIds.value.map((projectId, index) => ({
      project_id: projectId,
      priority: maxPriority + index + 1,
    }))

    await addProjectGroups(props.group.id, { projects })
    ElMessage.success('添加项目关联成功')
    addDialogVisible.value = false
    fetchProjectGroups()
    emit('success')
  }
  catch (error) {
    ElMessage.error('添加项目关联失败')
    console.error(error)
  }
  finally {
    submitting.value = false
  }
}

// 移除选中项目
async function handleRemoveProjects() {
  if (selectedProjects.value.length === 0) {
    ElMessage.warning('请选择要移除的项目关联')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要移除选中的 ${selectedProjects.value.length} 个项目关联吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await removeProjectGroups(props.group!.id, {
      project_group_ids: selectedProjects.value,
    })
    ElMessage.success('移除项目关联成功')
    selectedProjects.value = []
    fetchProjectGroups()
    emit('success')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除项目关联失败')
      console.error(error)
    }
  }
}

// 移除单个项目
async function handleRemoveProject(projectGroup: AicrProjectGroup) {
  try {
    await ElMessageBox.confirm(
      `确定要移除项目"${projectGroup.project_name}"的关联吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await removeProjectGroups(props.group!.id, {
      project_group_ids: [projectGroup.id],
    })
    ElMessage.success('移除项目关联成功')
    fetchProjectGroups()
    emit('success')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除项目关联失败')
      console.error(error)
    }
  }
}

// 调整优先级
async function adjustPriority(projectGroup: AicrProjectGroup, direction: 'up' | 'down') {
  const currentIndex = projectGroups.value.findIndex(pg => pg.id === projectGroup.id)
  if (
    (direction === 'up' && currentIndex === 0)
    || (direction === 'down' && currentIndex === projectGroups.value.length - 1)
  ) {
    return
  }

  const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
  const targetProject = projectGroups.value[targetIndex]

  // 交换优先级
  const tempPriority = projectGroup.priority
  projectGroup.priority = targetProject.priority
  targetProject.priority = tempPriority

  // 重新排序
  projectGroups.value.sort((a, b) => a.priority - b.priority)

  // 这里应该调用API更新优先级，但为了简化，我们先在前端处理
  // 实际项目中需要后端提供更新优先级的API
}

// 格式化时间
function formatTime(time: string) {
  return new Date(time).toLocaleString()
}

// 关闭对话框
function handleClose() {
  visible.value = false
  selectedProjects.value = []
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal && props.group) {
    fetchProjectGroups()
  }
})
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="`管理项目关联 - ${group?.name}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="project-management">
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAddProjects">
          <i class="i-ep-plus" />
          添加项目
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedProjects.length === 0"
          @click="handleRemoveProjects"
        >
          <i class="i-ep-delete" />
          移除选中
        </el-button>
        <span class="project-count">
          共 {{ projectGroups.length }} 个关联项目
        </span>
      </div>

      <!-- 优先级说明 -->
      <el-alert
        title="优先级说明"
        description="数字越小优先级越高，当项目同时关联多个群组时，优先使用优先级高的群组进行通知"
        type="info"
        :closable="false"
        show-icon
        class="priority-tip"
      />

      <!-- 项目列表 -->
      <el-table
        v-loading="loading"
        :data="projectGroups"
        border
        style="width: 100%"
        @selection-change="(selection: AicrProjectGroup[]) => selectedProjects = selection.map(s => s.id)"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template #default="{ row }">
            <el-tag type="primary" size="small">
              {{ row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="project_name" label="项目名称" min-width="150" />
        <el-table-column prop="create_time" label="关联时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template #default="{ row, $index }">
            <el-button
              text
              type="primary"
              size="small"
              :disabled="$index === 0"
              @click="adjustPriority(row, 'up')"
            >
              上移
            </el-button>
            <el-button
              text
              type="primary"
              size="small"
              :disabled="$index === projectGroups.length - 1"
              @click="adjustPriority(row, 'down')"
            >
              下移
            </el-button>
            <el-button
              text
              type="danger"
              size="small"
              @click="handleRemoveProject(row)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          关闭
        </el-button>
      </div>
    </template>

    <!-- 添加项目对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加项目关联"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="选择项目">
          <el-select
            v-model="selectedProjectIds"
            multiple
            filterable
            placeholder="请选择要关联的项目"
            style="width: 100%"
            :loading="loadingProjects"
          >
            <el-option
              v-for="project in availableProjects"
              :key="project.value"
              :label="project.label"
              :value="project.value"
            />
          </el-select>
          <div class="form-tip">
            只显示未关联该群组的项目，新添加的项目将按顺序分配优先级
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="submitting"
            :disabled="selectedProjectIds.length === 0"
            @click="confirmAddProjects"
          >
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style scoped>
.project-management {
  min-height: 400px;
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.project-count {
  margin-left: auto;
  color: #909399;
  font-size: 14px;
}

.priority-tip {
  margin-bottom: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
