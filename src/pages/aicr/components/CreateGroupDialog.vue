<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { CreateAicrGroupRequest } from '~/apis/aicr'
import { createAicrGroup } from '~/apis/aicr'
import { fetchAllUsers } from '~/apis/enums'
import { fetchProjectList } from '~/apis/project'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单数据
const form = reactive<CreateAicrGroupRequest>({
  name: '',
  type: 'normal',
  webhook_url: '',
  description: '',
  member_user_ids: [],
  project_ids: [],
})

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const submitting = ref(false)

// 用户选项
const userOptions = ref<Array<{ value: string, label: string }>>([])
const loadingUsers = ref(false)

// 项目选项
const projectOptions = ref<Array<{ value: string, label: string }>>([])
const loadingProjects = ref(false)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' },
  ],
  webhook_url: [
    { required: true, message: '请输入Webhook地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
  ],
  member_user_ids: [
    {
      validator: (rule, value, callback) => {
        if (form.type === 'normal' && (!value || value.length === 0)) {
          callback(new Error('普通群至少需要添加一个成员'))
        }
        else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  project_ids: [
    {
      validator: (rule, value, callback) => {
        if (form.type === 'project' && (!value || value.length === 0)) {
          callback(new Error('项目群至少需要关联一个项目'))
        }
        else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
}

// 获取用户列表
async function fetchUsers() {
  if (userOptions.value.length > 0)
    return

  loadingUsers.value = true
  try {
    const users = await fetchAllUsers()
    userOptions.value = users.map(user => ({
      value: user.value,
      label: user.label,
    }))
  }
  catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error(error)
  }
  finally {
    loadingUsers.value = false
  }
}

// 获取项目列表
async function fetchProjects() {
  if (projectOptions.value.length > 0)
    return

  loadingProjects.value = true
  try {
    const res = await fetchProjectList({ page: 1, pageSize: 1000 })
    projectOptions.value = res.list.map(project => ({
      value: project.id,
      label: project.name,
    }))
  }
  catch (error) {
    ElMessage.error('获取项目列表失败')
    console.error(error)
  }
  finally {
    loadingProjects.value = false
  }
}

// 群组类型变化
function handleTypeChange() {
  // 清空相关字段
  form.member_user_ids = []
  form.project_ids = []

  // 根据类型加载对应数据
  if (form.type === 'normal') {
    fetchUsers()
  }
  else {
    fetchProjects()
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()
    submitting.value = true

    await createAicrGroup(form)
    ElMessage.success('创建成功')
    emit('success')
    handleClose()
  }
  catch (error) {
    if (error !== false) { // 不是验证失败
      ElMessage.error('创建失败')
      console.error(error)
    }
  }
  finally {
    submitting.value = false
  }
}

// 关闭对话框
function handleClose() {
  visible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    type: 'normal',
    webhook_url: '',
    description: '',
    member_user_ids: [],
    project_ids: [],
  })
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal) {
    // 默认加载用户列表（普通群）
    fetchUsers()
  }
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="创建AICR群组"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="群组名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入群组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="群组类型" prop="type">
        <el-radio-group v-model="form.type" @change="handleTypeChange">
          <el-radio label="normal">
            普通群
          </el-radio>
          <el-radio label="project">
            项目群
          </el-radio>
        </el-radio-group>
        <div class="form-tip">
          <span v-if="form.type === 'normal'">普通群：支持添加多个人员，用于一般的代码审查通知</span>
          <span v-else>项目群：为指定项目配置专用群，优先级高于普通群</span>
        </div>
      </el-form-item>

      <el-form-item label="Webhook地址" prop="webhook_url">
        <el-input
          v-model="form.webhook_url"
          placeholder="请输入飞书群机器人Webhook地址"
          type="url"
        />
        <div class="form-tip">
          飞书群机器人的Webhook地址，用于发送代码审查通知
        </div>
      </el-form-item>

      <el-form-item label="群组描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入群组描述（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 普通群成员选择 -->
      <el-form-item
        v-if="form.type === 'normal'"
        label="群组成员"
        prop="member_user_ids"
      >
        <el-select
          v-model="form.member_user_ids"
          multiple
          filterable
          placeholder="请选择群组成员"
          style="width: 100%"
          :loading="loadingUsers"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.value"
            :label="user.label"
            :value="user.value"
          />
        </el-select>
      </el-form-item>

      <!-- 项目群项目选择 -->
      <el-form-item
        v-if="form.type === 'project'"
        label="关联项目"
        prop="project_ids"
      >
        <el-select
          v-model="form.project_ids"
          multiple
          filterable
          placeholder="请选择关联项目"
          style="width: 100%"
          :loading="loadingProjects"
        >
          <el-option
            v-for="project in projectOptions"
            :key="project.value"
            :label="project.label"
            :value="project.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
