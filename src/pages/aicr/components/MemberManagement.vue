<script setup lang="ts">
import type { AicrGroupConfig, AicrGroupMember } from '~/apis/aicr'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addGroupMembers, getAicrGroupById, removeGroupMembers } from '~/apis/aicr'
import { fetchAllUsers } from '~/apis/enums'

interface Props {
  modelValue: boolean
  group: AicrGroupConfig | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 数据状态
const loading = ref(false)
const members = ref<AicrGroupMember[]>([])
const selectedMembers = ref<string[]>([])

// 添加成员相关
const addDialogVisible = ref(false)
const selectedUsers = ref<string[]>([])
const userOptions = ref<Array<{ value: string, label: string }>>([])
const loadingUsers = ref(false)
const submitting = ref(false)

// 获取群组成员列表
async function fetchMembers() {
  if (!props.group?.id)
    return

  loading.value = true
  try {
    const groupData = await getAicrGroupById(props.group.id)
    members.value = groupData.members || []
  }
  catch (error) {
    ElMessage.error('获取成员列表失败')
    console.error(error)
  }
  finally {
    loading.value = false
  }
}

// 获取用户列表
async function fetchUsers() {
  if (userOptions.value.length > 0)
    return

  loadingUsers.value = true
  try {
    const users = await fetchAllUsers()
    userOptions.value = users.map(user => ({
      value: user.value,
      label: user.label,
    }))
  }
  catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error(error)
  }
  finally {
    loadingUsers.value = false
  }
}

// 获取可添加的用户（排除已有成员）
const availableUsers = computed(() => {
  const existingUserIds = members.value.map(m => m.user_id)
  return userOptions.value.filter(user => !existingUserIds.includes(user.value))
})

// 打开添加成员对话框
function handleAddMembers() {
  selectedUsers.value = []
  addDialogVisible.value = true
  fetchUsers()
}

// 确认添加成员
async function confirmAddMembers() {
  if (!props.group?.id || selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要添加的成员')
    return
  }

  submitting.value = true
  try {
    await addGroupMembers(props.group.id, {
      user_ids: selectedUsers.value,
    })
    ElMessage.success('添加成员成功')
    addDialogVisible.value = false
    fetchMembers()
    emit('success')
  }
  catch (error) {
    ElMessage.error('添加成员失败')
    console.error(error)
  }
  finally {
    submitting.value = false
  }
}

// 移除选中成员
async function handleRemoveMembers() {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请选择要移除的成员')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要移除选中的 ${selectedMembers.value.length} 个成员吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await removeGroupMembers(props.group!.id, {
      member_ids: selectedMembers.value,
    })
    ElMessage.success('移除成员成功')
    selectedMembers.value = []
    fetchMembers()
    emit('success')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除成员失败')
      console.error(error)
    }
  }
}

// 移除单个成员
async function handleRemoveMember(member: AicrGroupMember) {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员"${member.user_name}"吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await removeGroupMembers(props.group!.id, {
      member_ids: [member.id],
    })
    ElMessage.success('移除成员成功')
    fetchMembers()
    emit('success')
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除成员失败')
      console.error(error)
    }
  }
}

// 格式化时间
function formatTime(time: string) {
  return new Date(time).toLocaleString()
}

// 关闭对话框
function handleClose() {
  visible.value = false
  selectedMembers.value = []
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal && props.group) {
    fetchMembers()
  }
})
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="`管理成员 - ${group?.name}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="member-management">
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAddMembers">
          <i class="i-ep-plus" />
          添加成员
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedMembers.length === 0"
          @click="handleRemoveMembers"
        >
          <i class="i-ep-delete" />
          移除选中
        </el-button>
        <span class="member-count">
          共 {{ members.length }} 个成员
        </span>
      </div>

      <!-- 成员列表 -->
      <el-table
        v-loading="loading"
        :data="members"
        border
        style="width: 100%"
        @selection-change="(selection: AicrGroupMember[]) => selectedMembers = selection.map(s => s.id)"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="user_name" label="姓名" width="120" />
        <el-table-column prop="feishu_id" label="飞书ID" width="150" />
        <el-table-column prop="create_time" label="加入时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button
              text
              type="danger"
              size="small"
              @click="handleRemoveMember(row)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          关闭
        </el-button>
      </div>
    </template>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加成员"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="选择成员">
          <el-select
            v-model="selectedUsers"

            filterable multiple
            placeholder="请选择要添加的成员"
            style="width: 100%"
            :loading="loadingUsers"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
          <div class="form-tip">
            只显示未加入该群组的用户
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="submitting"
            :disabled="selectedUsers.length === 0"
            @click="confirmAddMembers"
          >
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style scoped>
.member-management {
  min-height: 400px;
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.member-count {
  margin-left: auto;
  color: #909399;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
