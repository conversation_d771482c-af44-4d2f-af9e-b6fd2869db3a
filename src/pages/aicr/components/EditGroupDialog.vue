<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { AicrGroupConfig, UpdateAicrGroupRequest } from '~/apis/aicr'
import { ElMessage } from 'element-plus'
import { updateAicrGroup } from '~/apis/aicr'
import { useUserStore } from '~/stores/user'

interface Props {
  modelValue: boolean
  group: AicrGroupConfig | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单数据
const form = reactive<UpdateAicrGroupRequest>({
  id: '',
  name: '',
  type: 'normal',
  webhook_url: '',
  description: '',
})

// 表单引用
const formRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  webhook_url: [
    { required: true, message: '请输入Webhook地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
  ],
}

// 检查编辑权限
const canEdit = computed(() => {
  return props.group?.creator === userStore.userInfo.id
})

// 初始化表单数据
function initForm() {
  if (props.group) {
    Object.assign(form, {
      id: props.group.id,
      name: props.group.name,
      type: props.group.type,
      webhook_url: props.group.webhook_url,
      description: props.group.description || '',
    })
  }
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value || !canEdit.value)
    return

  try {
    await formRef.value.validate()
    submitting.value = true

    await updateAicrGroup(form)
    ElMessage.success('更新成功')
    emit('success')
    handleClose()
  }
  catch (error) {
    if (error !== false) { // 不是验证失败
      ElMessage.error('更新失败')
      console.error(error)
    }
  }
  finally {
    submitting.value = false
  }
}

// 关闭对话框
function handleClose() {
  visible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: '',
    name: '',
    type: 'normal',
    webhook_url: '',
    description: '',
  })
}

// 监听对话框打开
watch(visible, (newVal) => {
  if (newVal && props.group) {
    nextTick(() => {
      initForm()
    })
  }
})

// 格式化群组类型
function formatGroupType(type: string | undefined) {
  if (!type)
    return ''
  return type === 'normal' ? '普通群' : '项目群'
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑AICR群组"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="!canEdit" class="permission-warning">
      <el-alert
        title="权限不足"
        description="只有群组创建者可以编辑群组信息"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :disabled="!canEdit"
      label-width="120px"
    >
      <el-form-item label="群组ID">
        <el-input v-model="form.id" disabled />
      </el-form-item>

      <el-form-item label="群组类型">
        <el-tag :type="form.type === 'normal' ? 'primary' : 'success'">
          {{ formatGroupType(form.type) }}
        </el-tag>
        <div class="form-tip">
          群组类型创建后不可修改
        </div>
      </el-form-item>

      <el-form-item label="群组名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入群组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="Webhook地址" prop="webhook_url">
        <el-input
          v-model="form.webhook_url"
          placeholder="请输入飞书群机器人Webhook地址"
          type="url"
        />
        <div class="form-tip">
          飞书群机器人的Webhook地址，用于发送代码审查通知
        </div>
      </el-form-item>

      <el-form-item label="群组描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入群组描述（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item v-if="group" label="成员/项目数">
        <span v-if="group.type === 'normal'">
          {{ group.members?.length || 0 }} 个成员
        </span>
        <span v-else>
          {{ group.projectGroups?.length || 0 }} 个关联项目
        </span>
        <div class="form-tip">
          <span v-if="group.type === 'normal'">
            成员管理请使用"管理成员"功能
          </span>
          <span v-else>
            项目关联管理请使用"管理项目"功能
          </span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          v-if="canEdit"
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.permission-warning {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
