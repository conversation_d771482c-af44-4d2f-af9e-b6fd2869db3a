<script setup lang="ts">
import type { UserInfoType } from '~/apis/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { delUserApi, getUserListApi } from '~/apis/user'
import EditUserPopup from '~/pages/user/components/editUserPopup.vue'
import { belongList, roleList } from '~/pages/user/utils'
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()
const list = ref<UserInfoType[]>([])
const page = ref(1)
const pageSize = ref(20)
const total = ref(0)

async function fetch() {
  const res = await getUserListApi({
    page: page.value,
    pageSize: pageSize.value,
  })
  list.value = res.list
  total.value = res.total
}
fetch()

function handleSizeChange(val: number) {
  pageSize.value = val
  page.value = 1
  fetch()
}

function handleCurrentChange(val: number) {
  page.value = val
  fetch()
}

function formatUserRole(row: UserInfoType) {
  const curRole = roleList.find((item) => {
    return item.value === row.role
  })
  return curRole ? curRole.label : '--'
}
function formatUserBelong(row: UserInfoType) {
  const curBelong = belongList.find((item) => {
    return item.value === row.belong
  })
  return curBelong ? curBelong.label : '--'
}

function deleteClick(row: UserInfoType) {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await delUserApi(row.id)
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    page.value = 1
    await fetch()
  }).catch(() => {
    ElMessage({
      message: '删除取消',
      type: 'info',
    })
  })
}

const dialogForm = ref(false)
const curRow = ref<UserInfoType>()
function editClick(row: UserInfoType) {
  curRow.value = row
  dialogForm.value = true
}
function closeDialog() {
  dialogForm.value = false
  page.value = 1
  fetch()
  userStore.getUserInfo()
}
</script>

<template>
  <h2>用户信息管理</h2>
  <div class="my-40px">
    <el-table
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="name"
        label="姓名"
        align="center"
      />
      <el-table-column
        prop="role"
        label="角色"
        align="center"
        :formatter="formatUserRole"
      />
      <el-table-column
        prop="belong"
        label="业务归属"
        align="center"
        :formatter="formatUserBelong"
      />
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button text type="primary" @click="editClick(scope.row)">
            编辑
          </el-button>
          <el-button text type="primary" :disabled="scope.row.id === userStore.userInfo.id" @click="deleteClick(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt-20px flex items-center justify-center">
      <el-pagination
        v-model:current-page="page"
        background
        :page-sizes="[20, 40, 80, 100]"
        :page-size="20"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <EditUserPopup :dialog-form="dialogForm" :manually-close="true" :raw-data="curRow" :page-type="true" @close-dialog="closeDialog" />
</template>

<route lang="yaml">
  meta:
    menu:
      name: 用户
      sort: 9
      path: /user/
      icon: i-solar-user-outline
</route>
