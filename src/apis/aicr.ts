import axios from '~/helpers/request'

// AICR群组配置类型定义
export interface AicrGroupConfig {
  id: string
  name: string
  type: 'normal' | 'project'
  webhook_url: string
  description?: string
  creator: string
  update_user: string
  create_time: string
  update_time: string
  members?: AicrGroupMember[]
  projectGroups?: AicrProjectGroup[]
}

// AICR群组成员类型定义
export interface AicrGroupMember {
  id: string
  group_id: string
  user_id: string
  feishu_id: string
  user_name: string
  create_time: string
}

// AICR项目群关联类型定义
export interface AicrProjectGroup {
  id: string
  group_id: string
  project_id: string
  project_name: string
  priority: number
  create_time: string
}

// 创建群组请求参数
export interface CreateAicrGroupRequest {
  name: string
  type: 'normal' | 'project'
  webhook_url: string
  description?: string
  member_user_ids?: string[]
  project_ids?: string[]
}

// 更新群组请求参数
export interface UpdateAicrGroupRequest {
  id: string
  name?: string
  type?: 'normal' | 'project'
  webhook_url?: string
  description?: string
}

// 查询群组请求参数
export interface FindAicrGroupRequest {
  page: number
  pageSize: number
  name?: string
  type?: string
  creator?: string
}

// 群组列表响应
export interface AicrGroupListResponse {
  page: number
  pageSize: number
  total: number
  list: AicrGroupConfig[]
}

// 添加群组成员请求参数
export interface AddGroupMemberRequest {
  user_ids: string[]
}

// 移除群组成员请求参数
export interface RemoveGroupMemberRequest {
  member_ids: string[]
}

// 项目群关联项
export interface ProjectGroupItem {
  project_id: string
  priority?: number
}

// 添加项目关联请求参数
export interface AddProjectGroupRequest {
  projects: ProjectGroupItem[]
}

// 移除项目关联请求参数
export interface RemoveProjectGroupRequest {
  project_group_ids: string[]
}

// API接口函数

/**
 * 获取AICR群组列表（分页）
 */
export function getAicrGroupList(params: FindAicrGroupRequest): Promise<AicrGroupListResponse> {
  return axios.get('/aicr/groups', { params })
}

/**
 * 根据ID获取AICR群组详情
 */
export function getAicrGroupById(id: string): Promise<AicrGroupConfig> {
  return axios.get(`/aicr/groups/${id}`)
}

/**
 * 创建AICR群组配置
 */
export function createAicrGroup(data: CreateAicrGroupRequest): Promise<AicrGroupConfig> {
  return axios.post('/aicr/groups', data)
}

/**
 * 更新AICR群组配置
 */
export function updateAicrGroup(data: UpdateAicrGroupRequest): Promise<AicrGroupConfig> {
  return axios.put('/aicr/groups', data)
}

/**
 * 删除AICR群组配置
 */
export function deleteAicrGroup(id: string): Promise<{ message: string }> {
  return axios.delete(`/aicr/groups/${id}`)
}

/**
 * 添加群组成员（仅普通群）
 */
export function addGroupMembers(groupId: string, data: AddGroupMemberRequest): Promise<AicrGroupConfig> {
  return axios.post(`/aicr/groups/${groupId}/members`, data)
}

/**
 * 移除群组成员（仅普通群）
 */
export function removeGroupMembers(groupId: string, data: RemoveGroupMemberRequest): Promise<AicrGroupConfig> {
  return axios.delete(`/aicr/groups/${groupId}/members`, { data })
}

/**
 * 添加项目关联（仅项目群）
 */
export function addProjectGroups(groupId: string, data: AddProjectGroupRequest): Promise<AicrGroupConfig> {
  return axios.post(`/aicr/groups/${groupId}/projects`, data)
}

/**
 * 移除项目关联（仅项目群）
 */
export function removeProjectGroups(groupId: string, data: RemoveProjectGroupRequest): Promise<AicrGroupConfig> {
  return axios.delete(`/aicr/groups/${groupId}/projects`, { data })
}

/**
 * 根据项目ID获取对应的AICR群配置
 */
export function getGroupsByProjectId(projectId: string): Promise<AicrGroupConfig[]> {
  return axios.get(`/aicr/projects/${projectId}/groups`)
}
