import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, FindOptionsWhere, Like, In } from 'typeorm'
import { AicrGroupConfig } from './entities/aicr-group-config.entity'
import { AicrGroupMember } from './entities/aicr-group-member.entity'
import { AicrProjectGroup } from './entities/aicr-project-group.entity'
import { CreateAicrGroupDto } from './dto/create-aicr-group.dto'
import { UpdateAicrGroupDto } from './dto/update-aicr-group.dto'
import { FindAicrGroupDto } from './dto/find-aicr-group.dto'
import {
  AddGroupMemberDto,
  RemoveGroupMemberDto
} from './dto/add-group-member.dto'
import {
  AddProjectGroupDto,
  RemoveProjectGroupDto
} from './dto/add-project-group.dto'
import { LoggerService } from 'src/logger/logger.service'
import { UserService } from 'src/user/user.service'
import { ProjectServer } from 'src/project/project.service'

@Injectable()
export class AicrService {
  constructor(
    @InjectRepository(AicrGroupConfig)
    private readonly groupConfigRepository: Repository<AicrGroupConfig>,
    @InjectRepository(AicrGroupMember)
    private readonly groupMemberRepository: Repository<AicrGroupMember>,
    @InjectRepository(AicrProjectGroup)
    private readonly projectGroupRepository: Repository<AicrProjectGroup>,
    private readonly logger: LoggerService,
    private readonly userService: UserService,
    private readonly projectService: ProjectServer
  ) {}

  /**
   * 创建AICR群组配置
   */
  async createGroup(createDto: CreateAicrGroupDto, userId: string) {
    // 检查群组名称是否已存在
    const existingGroup = await this.groupConfigRepository.findOne({
      where: { name: createDto.name }
    })
    if (existingGroup) {
      throw new HttpException('群组名称已存在', HttpStatus.BAD_REQUEST)
    }

    // 创建群组配置
    const groupConfig = this.groupConfigRepository.create({
      name: createDto.name,
      type: createDto.type,
      webhook_url: createDto.webhook_url,
      description: createDto.description,
      creator: userId,
      update_user: userId
    })

    const savedGroup = await this.groupConfigRepository.save(groupConfig)

    // 如果是普通群，添加成员
    if (createDto.type === 'normal' && createDto.member_user_ids?.length) {
      await this.addMembersToGroup(savedGroup.id, createDto.member_user_ids)
    }

    // 如果是项目群，添加项目关联
    if (createDto.type === 'project' && createDto.project_ids?.length) {
      const projects = createDto.project_ids.map((projectId, index) => ({
        project_id: projectId,
        priority: index + 1
      }))
      await this.addProjectsToGroup(savedGroup.id, projects)
    }

    this.logger.append(savedGroup.id, '创建AICR群组', '创建AICR群组', userId)
    return this.findGroupById(savedGroup.id)
  }

  /**
   * 分页查询群组列表
   */
  async findGroups(query: FindAicrGroupDto) {
    const findQuery: FindOptionsWhere<AicrGroupConfig> = {}

    if (query.name) {
      findQuery.name = Like(`%${query.name}%`)
    }
    if (query.type) {
      findQuery.type = query.type
    }
    if (query.creator) {
      findQuery.creator = query.creator
    }

    const [list, total] = await this.groupConfigRepository.findAndCount({
      where: findQuery,
      relations: ['members', 'projectGroups'],
      take: query.pageSize,
      skip: (query.page - 1) * query.pageSize,
      order: { create_time: 'DESC' }
    })

    return {
      page: query.page,
      pageSize: query.pageSize,
      list,
      total
    }
  }

  /**
   * 根据ID获取群组详情
   */
  async findGroupById(id: string) {
    const group = await this.groupConfigRepository.findOne({
      where: { id },
      relations: ['members', 'projectGroups']
    })

    if (!group) {
      throw new HttpException('群组不存在', HttpStatus.NOT_FOUND)
    }

    return group
  }

  /**
   * 更新群组配置
   */
  async updateGroup(updateDto: UpdateAicrGroupDto, userId: string) {
    const group = await this.findGroupById(updateDto.id)

    // 检查权限（只有创建者可以修改）
    if (group.creator !== userId) {
      throw new HttpException('无权限修改此群组', HttpStatus.FORBIDDEN)
    }

    // 如果修改了名称，检查是否重复
    if (updateDto.name && updateDto.name !== group.name) {
      const existingGroup = await this.groupConfigRepository.findOne({
        where: { name: updateDto.name }
      })
      if (existingGroup) {
        throw new HttpException('群组名称已存在', HttpStatus.BAD_REQUEST)
      }
    }

    await this.groupConfigRepository.update(updateDto.id, {
      ...updateDto,
      update_user: userId
    })

    this.logger.append(updateDto.id, '更新AICR群组', '更新AICR群组', userId)
    return this.findGroupById(updateDto.id)
  }

  /**
   * 删除群组
   */
  async deleteGroup(id: string, userId: string) {
    const group = await this.findGroupById(id)

    // 检查权限（只有创建者可以删除）
    if (group.creator !== userId) {
      throw new HttpException('无权限删除此群组', HttpStatus.FORBIDDEN)
    }

    await this.groupConfigRepository.update(id, { update_user: userId })
    await this.groupConfigRepository.softDelete(id)

    this.logger.append(id, '删除AICR群组', '删除AICR群组', userId)
    return { message: '删除成功' }
  }

  /**
   * 添加群组成员
   */
  async addGroupMembers(groupId: string, addDto: AddGroupMemberDto, userId: string) {
    const group = await this.findGroupById(groupId)

    if (group.type !== 'normal') {
      throw new HttpException('只有普通群可以添加成员', HttpStatus.BAD_REQUEST)
    }

    await this.addMembersToGroup(groupId, addDto.user_ids)
    this.logger.append(groupId, '添加群组成员', '添加群组成员', userId)
    return this.findGroupById(groupId)
  }

  /**
   * 移除群组成员
   */
  async removeGroupMembers(groupId: string, removeDto: RemoveGroupMemberDto, userId: string) {
    await this.groupMemberRepository.delete({
      id: In(removeDto.member_ids),
      group_id: groupId
    })

    this.logger.append(groupId, '移除群组成员', '移除群组成员', userId)
    return this.findGroupById(groupId)
  }

  /**
   * 添加项目关联
   */
  async addProjectGroups(groupId: string, addDto: AddProjectGroupDto, userId: string) {
    const group = await this.findGroupById(groupId)

    if (group.type !== 'project') {
      throw new HttpException('只有项目群可以添加项目关联', HttpStatus.BAD_REQUEST)
    }

    await this.addProjectsToGroup(groupId, addDto.projects)
    this.logger.append(groupId, '添加项目关联', '添加项目关联', userId)
    return this.findGroupById(groupId)
  }

  /**
   * 移除项目关联
   */
  async removeProjectGroups(groupId: string, removeDto: RemoveProjectGroupDto, userId: string) {
    await this.projectGroupRepository.delete({
      id: In(removeDto.project_group_ids),
      group_id: groupId
    })

    this.logger.append(groupId, '移除项目关联', '移除项目关联', userId)
    return this.findGroupById(groupId)
  }

  /**
   * 根据项目ID获取对应的AICR群配置（按优先级排序）
   */
  async getGroupConfigByProjectId(projectId: string) {
    const projectGroups = await this.projectGroupRepository.find({
      where: { project_id: projectId },
      relations: ['group'],
      order: { priority: 'ASC' }
    })

    return projectGroups.map(pg => pg.group)
  }

  /**
   * 根据用户邮箱获取用户所在的AICR群配置
   */
  async getGroupConfigByUserEmail(email: string) {
    // 先根据邮箱查找用户
    const user = await this.userService.userRepository.findOne({
      where: { email }
    })

    if (!user) {
      return {
        user: null,
        groups: []
      }
    }

    // 查找用户所在的群组
    const groupMembers = await this.groupMemberRepository.find({
      where: { user_id: user.id },
      relations: ['group']
    })

    const groups = groupMembers.map(gm => gm.group)

    return {
      user: {
        id: user.id,
        name: user.name,
        feishu_id: user.feishu_id,
        email: user.email
      },
      groups
    }
  }

  /**
   * 私有方法：添加成员到群组
   */
  private async addMembersToGroup(groupId: string, userIds: string[]) {
    const users = await this.userService.userRepository.find({
      where: { id: In(userIds) }
    })

    const members = users.map(user =>
      this.groupMemberRepository.create({
        group_id: groupId,
        user_id: user.id,
        feishu_id: user.feishu_id,
        user_name: user.name
      })
    )

    await this.groupMemberRepository.save(members)
  }

  /**
   * 私有方法：添加项目到群组
   */
  private async addProjectsToGroup(groupId: string, projects: Array<{project_id: string, priority?: number}>) {
    const projectIds = projects.map(p => p.project_id)
    const projectEntities = await this.projectService.projectModel.find({
      where: { id: In(projectIds) }
    })

    const projectGroups = projects.map(project => {
      const projectEntity = projectEntities.find(p => p.id === project.project_id)
      return this.projectGroupRepository.create({
        group_id: groupId,
        project_id: project.project_id,
        project_name: projectEntity?.name || '',
        priority: project.priority || 1
      })
    })

    await this.projectGroupRepository.save(projectGroups)
  }
}
