import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Query,
  Param,
  Request,
  HttpException,
  HttpStatus
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger'
import { AicrService } from './aicr.service'
import { CreateAicrGroupDto } from './dto/create-aicr-group.dto'
import { UpdateAicrGroupDto } from './dto/update-aicr-group.dto'
import { FindAicrGroupDto } from './dto/find-aicr-group.dto'
import {
  AddGroupMemberDto,
  RemoveGroupMemberDto
} from './dto/add-group-member.dto'
import {
  AddProjectGroupDto,
  RemoveProjectGroupDto
} from './dto/add-project-group.dto'

@ApiTags('AICR群配置')
@Controller('aicr')
export class AicrController {
  constructor(private readonly aicrService: AicrService) {}

  @ApiOperation({
    summary: '创建AICR群组配置'
  })
  @ApiResponse({
    status: 201,
    description: '创建成功'
  })
  @Post('/groups')
  async createGroup(
    @Request() req: any,
    @Body() createDto: CreateAicrGroupDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.createGroup(createDto, req.user.id)
  }

  @ApiOperation({
    summary: '获取AICR群组列表（分页）'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功'
  })
  @Get('/groups')
  async getGroups(@Query() query: FindAicrGroupDto) {
    return this.aicrService.findGroups(query)
  }

  @ApiOperation({
    summary: '根据ID获取AICR群组详情'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功'
  })
  @Get('/groups/:id')
  async getGroupById(@Param('id') id: string) {
    return this.aicrService.findGroupById(id)
  }

  @ApiOperation({
    summary: '更新AICR群组配置'
  })
  @ApiResponse({
    status: 200,
    description: '更新成功'
  })
  @Put('/groups')
  async updateGroup(
    @Request() req: any,
    @Body() updateDto: UpdateAicrGroupDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.updateGroup(updateDto, req.user.id)
  }

  @ApiOperation({
    summary: '删除AICR群组配置'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '删除成功'
  })
  @Delete('/groups/:id')
  async deleteGroup(@Request() req: any, @Param('id') id: string) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.deleteGroup(id, req.user.id)
  }

  @ApiOperation({
    summary: '添加群组成员（仅普通群）'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '添加成功'
  })
  @Post('/groups/:id/members')
  async addGroupMembers(
    @Request() req: any,
    @Param('id') id: string,
    @Body() addDto: AddGroupMemberDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.addGroupMembers(id, addDto, req.user.id)
  }

  @ApiOperation({
    summary: '移除群组成员（仅普通群）'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '移除成功'
  })
  @Delete('/groups/:id/members')
  async removeGroupMembers(
    @Request() req: any,
    @Param('id') id: string,
    @Body() removeDto: RemoveGroupMemberDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.removeGroupMembers(id, removeDto, req.user.id)
  }

  @ApiOperation({
    summary: '添加项目关联（仅项目群）'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '添加成功'
  })
  @Post('/groups/:id/projects')
  async addProjectGroups(
    @Request() req: any,
    @Param('id') id: string,
    @Body() addDto: AddProjectGroupDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.addProjectGroups(id, addDto, req.user.id)
  }

  @ApiOperation({
    summary: '移除项目关联（仅项目群）'
  })
  @ApiParam({
    name: 'id',
    description: '群组ID'
  })
  @ApiResponse({
    status: 200,
    description: '移除成功'
  })
  @Delete('/groups/:id/projects')
  async removeProjectGroups(
    @Request() req: any,
    @Param('id') id: string,
    @Body() removeDto: RemoveProjectGroupDto
  ) {
    if (!req.user?.id) {
      throw new HttpException('用户未登录', HttpStatus.UNAUTHORIZED)
    }
    return this.aicrService.removeProjectGroups(id, removeDto, req.user.id)
  }

  @ApiOperation({
    summary: '根据项目ID获取对应的AICR群配置'
  })
  @ApiParam({
    name: 'projectId',
    description: '项目ID'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功'
  })
  @Get('/projects/:projectId/groups')
  async getGroupsByProjectId(@Param('projectId') projectId: string) {
    return this.aicrService.getGroupConfigByProjectId(projectId)
  }

  @ApiOperation({
    summary: '根据用户邮箱获取用户所在的AICR群配置（AICR专用，无需token验证）'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功'
  })
  @Public()
  @Get('/users/email/groups')
  async getGroupsByUserEmail(@Query('email') email: string) {
    if (!email) {
      throw new HttpException('邮箱参数不能为空', HttpStatus.BAD_REQUEST)
    }
    return this.aicrService.getGroupConfigByUserEmail(email)
  }
}
