<script setup lang="ts">
import type { User } from '~/apis/enums'
import type { Pipeline } from '~/apis/pipeline'
import dayjs from 'dayjs'
import { fetchPipelineStages, stopPipeline } from '~/apis/pipeline'
import PipelineLog from '~/components/pipeline/log.vue'
import PipelineStage from '~/components/pipeline/stage.vue'
import PipelineStatus from '~/components/pipeline/status.vue'

const props = defineProps<{
  pipelines: Array<Pipeline>
  users: Array<User>
}>()

const emit = defineEmits(['refresh', 'release'])

function getUserName(row: Pipeline) {
  return props.users.find(item => item.value === row.last_operator)?.label
}

function getUpdateTime(row: Pipeline) {
  if (row.update_at) {
    return timeFormat(row.update_at)
  }
  if (row.create_at) {
    return timeFormat(row.create_at)
  }
  return '--'
}

function timeFormat(time: string) {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const showLog = ref(false)
const currentPipeline = ref<Pipeline | null>(null)

function openLog(row: Pipeline) {
  currentPipeline.value = row
  showLog.value = true
}

function openRelease(row: Pipeline) {
  emit('release', row)
}

function stop(id: string) {
  ElMessageBox.confirm('确认停止构建?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    stopPipeline(id).then(() => {
      ElMessage.success('停止构建成功!')
      emit('refresh')
    })
  }).catch(() => {
    ElMessage.info('已取消操作')
  })
}

function getPipelineStages(pipeline: Pipeline) {
  fetchPipelineStages(pipeline.id).then((res) => {
    pipeline.stages = res
    const stage = res.find(item => item.stage === 'docker-build' && item.status === 'success')
    pipeline.tag = stage?.job_data?.new_tag || '--'
  })
}

watch(() => props.pipelines, (newVal, oldVal) => {
  newVal.forEach((newPipeline) => {
    const oldPipeline = oldVal.find(p => p.id === newPipeline.id)
    if (oldPipeline) {
      newPipeline.stages = oldPipeline.stages
      newPipeline.tag = oldPipeline.tag
      if (newPipeline.state === 'pending') {
        getPipelineStages(newPipeline)
      }
      else if (newPipeline.state !== 'pending' && oldPipeline.state === 'pending') {
        getPipelineStages(newPipeline)
      }
    }
    else {
      getPipelineStages(newPipeline)
    }
  })
})
</script>

<template>
  <el-table :data="pipelines">
    <el-table-column prop="status" label="状态" align="center" width="150">
      <template #default="{ row }">
        <PipelineStatus :state="row.state" :count-time="row.count_time" />
      </template>
    </el-table-column>
    <el-table-column prop="pipeline" label="Pipeline" align="center" width="100">
      <template #default="{ row }">
        <div class="text-md">
          {{ row.tag_type }}
        </div>
        <div class="flex justify-center">
          <div class="flex items-center rounded-md bg-gray-200 px-1 text-sm">
            <div class="i-hugeicons-git-commit" />
            <div>{{ row.branch }}</div>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="operator" label="操作人" align="center" width="100">
      <template #default="{ row }">
        <div>{{ getUserName(row) }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="stages" label="Stages" align="center">
      <template #default="{ row }">
        <PipelineStage v-for="item in row.stages" :key="item.id" :pipeline-stage="item" />
      </template>
    </el-table-column>
    <el-table-column prop="tag" label="Tag名称" align="center" width="180">
      <template #default="{ row }">
        <div>{{ row.tag }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="updateTime" label="更新时间" align="center">
      <template #default="{ row }">
        <div>{{ getUpdateTime(row) }}</div>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="openLog(row)">
          构建日志
        </el-button>
        <el-button v-if="row.branch === 'master' && row.state === 'success'" type="primary" link @click="openRelease(row)">
          发起上线
        </el-button>
        <el-button v-if="row.state === 'pending'" type="primary" link @click="stop(row.id)">
          停止构建
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <PipelineLog :id="currentPipeline?.id" v-model="showLog" :need-fetch="currentPipeline?.state === 'pending'" />
</template>
