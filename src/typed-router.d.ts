/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/aicr/': RouteRecordInfo<'/aicr/', '/aicr', Record<never, never>, Record<never, never>>,
    '/aicr/components/CreateGroupDialog': RouteRecordInfo<'/aicr/components/CreateGroupDialog', '/aicr/components/CreateGroupDialog', Record<never, never>, Record<never, never>>,
    '/aicr/components/EditGroupDialog': RouteRecordInfo<'/aicr/components/EditGroupDialog', '/aicr/components/EditGroupDialog', Record<never, never>, Record<never, never>>,
    '/aicr/components/MemberManagement': RouteRecordInfo<'/aicr/components/MemberManagement', '/aicr/components/MemberManagement', Record<never, never>, Record<never, never>>,
    '/aicr/components/ProjectManagement': RouteRecordInfo<'/aicr/components/ProjectManagement', '/aicr/components/ProjectManagement', Record<never, never>, Record<never, never>>,
    '/build/': RouteRecordInfo<'/build/', '/build', Record<never, never>, Record<never, never>>,
    '/lit-c/info': RouteRecordInfo<'/lit-c/info', '/lit-c/info', Record<never, never>, Record<never, never>>,
    '/lit-c/npms': RouteRecordInfo<'/lit-c/npms', '/lit-c/npms', Record<never, never>, Record<never, never>>,
    '/lit-c/project': RouteRecordInfo<'/lit-c/project', '/lit-c/project', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/monitor/manage': RouteRecordInfo<'/monitor/manage', '/monitor/manage', Record<never, never>, Record<never, never>>,
    '/monitor/whitelist': RouteRecordInfo<'/monitor/whitelist', '/monitor/whitelist', Record<never, never>, Record<never, never>>,
    '/projects/': RouteRecordInfo<'/projects/', '/projects', Record<never, never>, Record<never, never>>,
    '/projects/components/ProjectForm': RouteRecordInfo<'/projects/components/ProjectForm', '/projects/components/ProjectForm', Record<never, never>, Record<never, never>>,
    '/projects/detail/': RouteRecordInfo<'/projects/detail/', '/projects/detail', Record<never, never>, Record<never, never>>,
    '/release/': RouteRecordInfo<'/release/', '/release', Record<never, never>, Record<never, never>>,
    '/release/feishu': RouteRecordInfo<'/release/feishu', '/release/feishu', Record<never, never>, Record<never, never>>,
    '/release/statistic': RouteRecordInfo<'/release/statistic', '/release/statistic', Record<never, never>, Record<never, never>>,
    '/report-chart/': RouteRecordInfo<'/report-chart/', '/report-chart', Record<never, never>, Record<never, never>>,
    '/report-chart/components/hawkeye/hawkeye': RouteRecordInfo<'/report-chart/components/hawkeye/hawkeye', '/report-chart/components/hawkeye/hawkeye', Record<never, never>, Record<never, never>>,
    '/report-chart/components/monitor-abnormal/jsErrorTop10': RouteRecordInfo<'/report-chart/components/monitor-abnormal/jsErrorTop10', '/report-chart/components/monitor-abnormal/jsErrorTop10', Record<never, never>, Record<never, never>>,
    '/report-chart/components/monitor-abnormal/resourceErrorTop10': RouteRecordInfo<'/report-chart/components/monitor-abnormal/resourceErrorTop10', '/report-chart/components/monitor-abnormal/resourceErrorTop10', Record<never, never>, Record<never, never>>,
    '/report-chart/components/monitor-app/bigImg': RouteRecordInfo<'/report-chart/components/monitor-app/bigImg', '/report-chart/components/monitor-app/bigImg', Record<never, never>, Record<never, never>>,
    '/report-chart/components/monitor-performance/pageInfo': RouteRecordInfo<'/report-chart/components/monitor-performance/pageInfo', '/report-chart/components/monitor-performance/pageInfo', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectEntry/appKey': RouteRecordInfo<'/report-chart/components/projectEntry/appKey', '/report-chart/components/projectEntry/appKey', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectEntry/pathname': RouteRecordInfo<'/report-chart/components/projectEntry/pathname', '/report-chart/components/projectEntry/pathname', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/': RouteRecordInfo<'/report-chart/components/projectInfo/', '/report-chart/components/projectInfo', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/loki': RouteRecordInfo<'/report-chart/components/projectInfo/loki', '/report-chart/components/projectInfo/loki', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/pagesBadFCP': RouteRecordInfo<'/report-chart/components/projectInfo/pagesBadFCP', '/report-chart/components/projectInfo/pagesBadFCP', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/pagesBlankScreen': RouteRecordInfo<'/report-chart/components/projectInfo/pagesBlankScreen', '/report-chart/components/projectInfo/pagesBlankScreen', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/pagesJsError': RouteRecordInfo<'/report-chart/components/projectInfo/pagesJsError', '/report-chart/components/projectInfo/pagesJsError', Record<never, never>, Record<never, never>>,
    '/report-chart/components/projectInfo/pagesResourceError': RouteRecordInfo<'/report-chart/components/projectInfo/pagesResourceError', '/report-chart/components/projectInfo/pagesResourceError', Record<never, never>, Record<never, never>>,
    '/user/': RouteRecordInfo<'/user/', '/user', Record<never, never>, Record<never, never>>,
    '/user/components/editUserPopup': RouteRecordInfo<'/user/components/editUserPopup', '/user/components/editUserPopup', Record<never, never>, Record<never, never>>,
    '/ycac/article': RouteRecordInfo<'/ycac/article', '/ycac/article', Record<never, never>, Record<never, never>>,
    '/ycac/components/Card': RouteRecordInfo<'/ycac/components/Card', '/ycac/components/Card', Record<never, never>, Record<never, never>>,
    '/ycac/components/WeeklyCard': RouteRecordInfo<'/ycac/components/WeeklyCard', '/ycac/components/WeeklyCard', Record<never, never>, Record<never, never>>,
    '/ycac/info': RouteRecordInfo<'/ycac/info', '/ycac/info', Record<never, never>, Record<never, never>>,
    '/ycac/weekly': RouteRecordInfo<'/ycac/weekly', '/ycac/weekly', Record<never, never>, Record<never, never>>,
  }
}
