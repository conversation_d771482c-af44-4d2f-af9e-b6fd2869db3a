import { UpdateUserDto } from './dto/udpate-user.dto'
import { Injectable, Inject } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { In, Repository } from 'typeorm'
import { AdminUser } from './entities/user.entity'
import { FindUserDto } from './dto/find-user.dto'
import { FindOptionsWhere, Like } from 'typeorm'
import { LoggerService } from 'src/logger/logger.service'
import { ConfigService } from '@nestjs/config'
import dayjs from 'dayjs'

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(AdminUser)
    public readonly userRepository: Repository<AdminUser>,
    private readonly logger: LoggerService,
    private readonly configService: ConfigService
  ) {}

  async findAllUser() {
    return this.userRepository.find({
      select: ['name', 'id', 'belong', 'role']
    })
  }

  /**
   * 获取值班运维信息
   */
  async getOnCallOfficer() {
    const officers = await this.userRepository.find({
      where: {
        role: '6',
        name: In(['张乾', '郭强'])
      }
    })
    if (!officers.length) {
      return this.userRepository.findOne({
        where: {
          name: '李辉'
        }
      })
    } else if (officers.length === 1) {
      return officers[0]
    } else {
      const week = dayjs().week()
      const duty = this.configService.get('dutyOrder')
      const officerOne = officers.find((el) => el.name === '张乾')
      const officerTwo = officers.find((el) => el.name === '郭强')
      return week % 2 === Number(duty) ? officerOne : officerTwo
    }
  }
  /**
   * 获取所有运维用户
   * @returns
   */
  async getAllOfficer() {
    return this.userRepository.find({
      where: {
        role: '6',
        name: In(['张乾', '郭强'])
      }
    })
  }

  async findUserPage(query: FindUserDto) {
    const findQuery: FindOptionsWhere<AdminUser> = {}
    const data = await this.userRepository.find({
      withDeleted: false,
      take: query.pageSize,
      skip: (query.page - 1) * query.pageSize
    })
    const count = await this.userRepository.countBy(findQuery)
    return {
      page: query.page,
      pageSize: query.pageSize,
      list: data,
      total: count
    }
  }

  async findUserById(id: string) {
    return this.userRepository.findOneBy({ id })
  }

  async findUserByFeishuId(id: string) {
    return this.userRepository.findOneBy({ feishu_id: id })
  }

  async findUserByEmail(email: string) {
    return this.userRepository.findOneBy({ email })
  }
  async createNewUser(name: string, feishu_id: string, avatar_url: string, email?: string) {
    const newUser = this.userRepository.create({
      name,
      feishu_id,
      status: '在职',
      avatar_url,
      email: email || ''
    })
    await this.userRepository.save(newUser)
    this.logger.append(newUser.id, '新用户登录创建', '新用户登录', newUser.id)

    return newUser
  }

  async updateUserEmail(userId: string, email: string) {
    await this.userRepository.update(userId, { email })
  }

  async updateUser(user_id: string, data: UpdateUserDto) {
    if (data.id) {
      const user = await this.userRepository.update(data.id, {
        ...data,
        update_user: user_id //为登录用户id
      })
      this.logger.append(data.id, '用户信息更新', '更新用户', user_id)
      return user
    } else {
      const newUserBody = { ...data }
      delete newUserBody.id
      const newUser = this.userRepository.create({
        ...newUserBody,
        update_user: user_id
      })
      await this.userRepository.save(newUser)
      return newUser
    }
  }

  async deleteUser(id: string, user_id: string) {
    await this.userRepository.update(id, {
      update_user: user_id
    })
    this.logger.append(id, '用户删除', '删除用户', user_id)
    await this.userRepository.softDelete(id)
  }
  async getUserFromRole(role: string) {
    return this.userRepository.findBy({
      role
    })
  }
  async getUserFromLoginUser(user_id: string) {
    const user = await this.userRepository.findOne({
      where: {
        id: user_id
      }
    })
    if (user) {
      const users = await this.userRepository.find({
        where: {
          role: user.role
        }
      })
      return users
    }
    return []
  }
}
