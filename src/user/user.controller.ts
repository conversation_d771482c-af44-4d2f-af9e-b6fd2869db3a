import {
  Body,
  Controller,
  Post,
  Get,
  Query,
  HttpException,
  HttpStatus,
  Req,
  Request
} from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { AuthService } from 'src/auth/auth.service'
import { Public } from 'src/auth/jwt-meta'
import { FindUserDto } from './dto/find-user.dto'
import { UpdateUserDto } from './dto/udpate-user.dto'
import { UserService } from './user.service'

@ApiTags('用户')
@Controller('user')
export class UserController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService
  ) {}
  @ApiOperation({
    summary: '飞书登录JWT接口，返回用户信息和token'
  })
  @Public()
  @Post('/login')
  async login(@Body('code') code: string) {
    const res = await this.authService.login(code)
    if (res) {
      return res
    } else {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: '登录失败'
        },
        HttpStatus.BAD_REQUEST
      )
    }
  }

  @ApiOperation({
    summary: '根据用户ID获取用户信息接口'
  })
  @Get()
  async getUserById(@Query('id') id: string) {
    return this.userService.findUserById(id)
  }

  @ApiOperation({
    summary: '获取当前登录用户信息,无参数'
  })
  @Get('/info')
  async getUserInfoFromToken(@Request() req: any) {
    const user = req.user
    if (user) {
      const userInfo = await this.userService.findUserByFeishuId(user.feishu_id)
      return userInfo
    }
  }

  @ApiOperation({
    summary: '用户列表分页接口'
  })
  @Get('/page')
  async getUserPageList(@Query() query: FindUserDto) {
    return this.userService.findUserPage(query)
  }

  @ApiOperation({
    summary: '用户信息编辑/新增'
  })
  @Post()
  async updateUser(@Request() req: any, @Body() body: UpdateUserDto) {
    if (req.user.id) {
      return this.userService.updateUser(req.user.id, body)
    }
  }

  @ApiOperation({
    summary: '删除用户'
  })
  @Get('/del')
  async deleteUserById(@Request() req: any, @Query('id') id: string) {
    if (req.user.id) {
      return this.userService.deleteUser(id, req.user.id)
    }
  }

  @ApiOperation({
    summary: '获取所有用户列表，用于选项回填'
  })
  @Get('/all')
  async getAllUsers() {
    const list = await this.userService.findAllUser()
    return list.map((user) => ({
      value: user.id,
      label: user.name,
      role: user.role,
      belong: user.belong
    }))
  }
  @ApiOperation({
    summary: '根据role获取用户列表',
    description: '1 开发，2 测试，3运维'
  })
  @Get('/role')
  async getUserFromRole(@Query('role') role: string) {
    return this.userService.getUserFromRole(role)
  }
  @ApiOperation({
    summary: '根据当前登录用户的role获取此role用户列表'
  })
  @Get('/currentUser')
  async getUserFromUserLogin(@Request() req: any) {
    if (req.user.id) {
      return this.userService.getUserFromLoginUser(req.user.id)
    }
  }

  @ApiOperation({
    summary: '根据邮箱获取用户信息（AICR专用，无需token验证）'
  })
  @Public()
  @Get('/email')
  async getUserByEmail(@Query('email') email: string) {
    if (!email) {
      throw new HttpException('邮箱参数不能为空', HttpStatus.BAD_REQUEST)
    }
    return this.userService.findUserByEmail(email)
  }
}
