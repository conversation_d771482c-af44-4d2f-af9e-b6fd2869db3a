#!/usr/bin/env python3
"""
JARVIS集成功能测试脚本
用于测试AI-CodeReview与JARVIS服务的集成功能
"""

import os
import sys
import json
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv('conf/.env')

from biz.utils.jarvis_client import JarvisClient, GitlabUserClient
from biz.utils.jarvis_integration import JarvisIntegrationService
from biz.utils.im.enhanced_feishu import EnhancedFeishuNotifier


def test_jarvis_client():
    """测试JARVIS客户端"""
    print("=== 测试JARVIS客户端 ===")
    
    client = JarvisClient()
    
    # 测试根据邮箱获取用户信息
    test_email = "<EMAIL>"
    print(f"测试邮箱: {test_email}")
    
    user_info = client.get_user_by_email(test_email)
    print(f"用户信息: {json.dumps(user_info, indent=2, ensure_ascii=False)}")
    
    # 测试根据邮箱获取群配置
    group_config = client.get_groups_by_user_email(test_email)
    print(f"群配置: {json.dumps(group_config, indent=2, ensure_ascii=False)}")
    
    print()


def test_gitlab_client():
    """测试GitLab客户端"""
    print("=== 测试GitLab客户端 ===")
    
    gitlab_url = os.getenv('GITLAB_URL', 'https://gitlab.example.com')
    gitlab_token = os.getenv('GITLAB_ACCESS_TOKEN', 'test-token')
    
    if not gitlab_token or gitlab_token == 'test-token':
        print("警告: 未配置有效的GITLAB_ACCESS_TOKEN，跳过GitLab客户端测试")
        return
    
    client = GitlabUserClient(gitlab_url, gitlab_token)
    
    # 测试根据用户名获取用户信息
    test_username = "testuser"
    print(f"测试用户名: {test_username}")
    
    user_info = client.get_user_by_username(test_username)
    print(f"用户信息: {json.dumps(user_info, indent=2, ensure_ascii=False)}")
    
    # 测试根据提交SHA获取邮箱
    test_project_id = "1"
    test_commit_sha = "abc123"
    print(f"测试项目ID: {test_project_id}, 提交SHA: {test_commit_sha}")
    
    email = client.get_commit_author_email(test_project_id, test_commit_sha)
    print(f"提交人邮箱: {email}")
    
    print()


def test_enhanced_feishu():
    """测试增强飞书通知器"""
    print("=== 测试增强飞书通知器 ===")
    
    webhook_url = os.getenv('FEISHU_WEBHOOK_URL')
    
    if not webhook_url or 'xxx' in webhook_url:
        print("警告: 未配置有效的FEISHU_WEBHOOK_URL，跳过飞书通知器测试")
        return
    
    notifier = EnhancedFeishuNotifier()
    
    # 测试发送代码审查消息
    success = notifier.send_code_review_message(
        webhook_url=webhook_url,
        title="🔍 测试项目 - AI代码审查",
        content="这是一个测试的代码审查结果。\n\n**主要问题:**\n1. 代码格式需要优化\n2. 建议添加注释",
        author_name="测试用户",
        author_feishu_id="ou_test123",
        project_name="测试项目",
        source_branch="feature/test",
        target_branch="main",
        mr_url="https://gitlab.example.com/test/project/-/merge_requests/1",
        at_users=["ou_test123"]
    )
    
    print(f"代码审查消息发送结果: {success}")
    
    # 测试发送简单消息
    success = notifier.send_simple_message(
        webhook_url=webhook_url,
        content="这是一个测试消息",
        title="测试通知",
        at_users=["ou_test123"]
    )
    
    print(f"简单消息发送结果: {success}")
    
    print()


def test_integration_service():
    """测试集成服务"""
    print("=== 测试JARVIS集成服务 ===")
    
    service = JarvisIntegrationService()
    
    # 模拟提交数据
    test_commits = [
        {
            "id": "abc123def456",
            "message": "feat: 添加新功能",
            "author_name": "testuser",
            "timestamp": "2024-01-01T10:00:00Z",
            "url": "https://gitlab.example.com/test/project/-/commit/abc123def456"
        }
    ]
    
    # 测试发送增强通知
    success = service.send_enhanced_notification(
        user_email="<EMAIL>",
        review_content="这是一个测试的AI审查结果。\n\n**建议:**\n1. 代码质量良好\n2. 建议添加单元测试",
        project_name="测试项目",
        author_name="测试用户",
        source_branch="feature/test",
        target_branch="main",
        mr_url="https://gitlab.example.com/test/project/-/merge_requests/1",
        commits=test_commits,
        url_slug="gitlab_example_com"
    )
    
    print(f"增强通知发送结果: {success}")
    
    # 测试发送Push通知
    success = service.send_push_notification(
        user_email="<EMAIL>",
        review_content="Push事件的AI审查结果。",
        project_name="测试项目",
        commits=test_commits,
        url_slug="gitlab_example_com"
    )
    
    print(f"Push通知发送结果: {success}")
    
    print()


def main():
    """主函数"""
    print("开始测试JARVIS集成功能...")
    print(f"JARVIS_BASE_URL: {os.getenv('JARVIS_BASE_URL', 'http://localhost:8400')}")
    print(f"JARVIS_INTEGRATION_ENABLED: {os.getenv('JARVIS_INTEGRATION_ENABLED', '0')}")
    print()
    
    try:
        # 测试各个组件
        test_jarvis_client()
        test_gitlab_client()
        test_enhanced_feishu()
        test_integration_service()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
