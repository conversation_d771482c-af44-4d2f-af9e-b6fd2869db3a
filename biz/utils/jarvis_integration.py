import os
from typing import Optional, Dict, Any, List, Tuple
from biz.utils.log import logger
from biz.utils.jarvis_client import JarvisClient, GitlabUserClient
from biz.utils.im.enhanced_feishu import EnhancedFeishuNotifier
from biz.utils.im import notifier


class JarvisIntegrationService:
    """JARVIS集成服务，负责协调JARVIS API调用和飞书消息发送"""
    
    def __init__(self):
        self.jarvis_client = JarvisClient()
        self.enhanced_feishu = EnhancedFeishuNotifier()
        self.jarvis_enabled = os.getenv('JARVIS_INTEGRATION_ENABLED', '0') == '1'
        
    def get_user_email_from_commits(
        self, 
        commits: List[Dict[str, Any]], 
        gitlab_token: str, 
        gitlab_url: str, 
        project_id: str
    ) -> Optional[str]:
        """
        从提交信息中获取用户邮箱
        :param commits: 提交列表
        :param gitlab_token: GitLab访问令牌
        :param gitlab_url: GitLab URL
        :param project_id: 项目ID
        :return: 用户邮箱
        """
        if not commits:
            return None
            
        gitlab_client = GitlabUserClient(gitlab_url, gitlab_token)
        
        # 尝试从最新的提交获取邮箱
        for commit in commits:
            commit_id = commit.get('id')
            if commit_id:
                email = gitlab_client.get_commit_author_email(project_id, commit_id)
                if email:
                    return email
                    
        # 如果从提交详情获取不到邮箱，尝试通过用户名获取
        for commit in commits:
            author_name = commit.get('author_name')
            if author_name:
                user_info = gitlab_client.get_user_by_username(author_name)
                if user_info and user_info.get('email'):
                    return user_info['email']
                    
        return None
    
    def get_jarvis_group_config(self, user_email: str) -> Tuple[Optional[Dict], List[Dict]]:
        """
        从JARVIS获取用户群配置
        :param user_email: 用户邮箱
        :return: (用户信息, 群配置列表)
        """
        if not self.jarvis_enabled or not user_email:
            return None, []
            
        try:
            result = self.jarvis_client.get_groups_by_user_email(user_email)
            if result:
                user_info = result.get('user')
                groups = result.get('groups', [])
                return user_info, groups
            else:
                return None, []
        except Exception as e:
            logger.error(f"获取JARVIS群配置失败: {str(e)}")
            return None, []
    
    def send_enhanced_notification(
        self,
        user_email: str,
        review_content: str,
        project_name: str,
        author_name: str,
        source_branch: str = None,
        target_branch: str = None,
        mr_url: str = None,
        commits: List[Dict] = None,
        gitlab_token: str = None,
        gitlab_url: str = None,
        project_id: str = None,
        url_slug: str = None
    ) -> bool:
        """
        发送增强的通知消息
        :param user_email: 用户邮箱
        :param review_content: 审查内容
        :param project_name: 项目名称
        :param author_name: 作者姓名
        :param source_branch: 源分支
        :param target_branch: 目标分支
        :param mr_url: MR链接
        :param commits: 提交列表
        :param gitlab_token: GitLab令牌
        :param gitlab_url: GitLab URL
        :param project_id: 项目ID
        :param url_slug: URL slug
        :return: 是否发送成功
        """
        logger.info(f"开始发送增强通知，用户邮箱: {user_email}")
        
        # 如果JARVIS集成未启用，使用原有逻辑
        if not self.jarvis_enabled:
            logger.info("JARVIS集成未启用，使用原有通知逻辑")
            return self._send_fallback_notification(
                review_content, project_name, author_name, source_branch, 
                target_branch, mr_url, url_slug
            )
        
        # 如果没有用户邮箱，尝试从提交中获取
        if not user_email and commits and gitlab_token and gitlab_url and project_id:
            user_email = self.get_user_email_from_commits(
                commits, gitlab_token, gitlab_url, project_id
            )
        
        # 如果还是没有邮箱，使用兜底逻辑
        if not user_email:
            logger.warning("无法获取用户邮箱，使用兜底通知逻辑")
            return self._send_fallback_notification(
                review_content, project_name, author_name, source_branch, 
                target_branch, mr_url, url_slug
            )
        
        # 从JARVIS获取群配置
        user_info, groups = self.get_jarvis_group_config(user_email)
        
        # 如果没有找到群配置，使用兜底逻辑
        if not groups:
            logger.info(f"用户 {user_email} 未配置JARVIS群，使用兜底通知逻辑")
            return self._send_fallback_notification(
                review_content, project_name, author_name, source_branch, 
                target_branch, mr_url, url_slug
            )
        
        # 发送到JARVIS配置的群
        success = False
        for group in groups:
            webhook_url = group.get('webhook_url')
            if webhook_url:
                # 获取需要艾特的用户列表
                at_users = []
                if user_info and user_info.get('feishu_id'):
                    at_users.append(user_info['feishu_id'])
                
                # 发送消息
                result = self.enhanced_feishu.send_code_review_message(
                    webhook_url=webhook_url,
                    title=f"🔍 {project_name} - AI代码审查",
                    content=review_content,
                    author_name=author_name,
                    author_feishu_id=user_info.get('feishu_id') if user_info else None,
                    project_name=project_name,
                    source_branch=source_branch,
                    target_branch=target_branch,
                    mr_url=mr_url,
                    at_users=at_users
                )
                
                if result:
                    success = True
                    logger.info(f"成功发送到JARVIS群: {group.get('name', 'Unknown')}")
                else:
                    logger.warning(f"发送到JARVIS群失败: {group.get('name', 'Unknown')}")
        
        # 如果所有JARVIS群都发送失败，使用兜底逻辑
        if not success:
            logger.warning("所有JARVIS群发送失败，使用兜底通知逻辑")
            return self._send_fallback_notification(
                review_content, project_name, author_name, source_branch, 
                target_branch, mr_url, url_slug
            )
        
        return success
    
    def _send_fallback_notification(
        self,
        review_content: str,
        project_name: str,
        author_name: str,
        source_branch: str = None,
        target_branch: str = None,
        mr_url: str = None,
        url_slug: str = None
    ) -> bool:
        """
        兜底通知逻辑，使用原有的通知方式
        """
        try:
            # 构建消息内容
            im_msg = f"""
### 🔀 {project_name}: Merge Request

#### 合并请求信息:
- **提交者:** {author_name}
- **源分支**: {source_branch or 'Unknown'}
- **目标分支**: {target_branch or 'Unknown'}

- [查看合并详情]({mr_url or '#'})

- **AI Review 结果:** 

{review_content}
            """
            
            # 使用原有的通知器发送
            notifier.send_notification(
                content=im_msg, 
                msg_type='markdown', 
                title='Merge Request Review',
                project_name=project_name,
                url_slug=url_slug
            )
            
            logger.info("兜底通知发送成功")
            return True
            
        except Exception as e:
            logger.error(f"兜底通知发送失败: {str(e)}")
            return False
    
    def send_push_notification(
        self,
        user_email: str,
        review_content: str,
        project_name: str,
        commits: List[Dict],
        gitlab_token: str = None,
        gitlab_url: str = None,
        project_id: str = None,
        url_slug: str = None
    ) -> bool:
        """
        发送Push事件的增强通知
        """
        logger.info(f"开始发送Push增强通知，用户邮箱: {user_email}")
        
        # 如果JARVIS集成未启用，使用原有逻辑
        if not self.jarvis_enabled:
            logger.info("JARVIS集成未启用，使用原有Push通知逻辑")
            return self._send_push_fallback_notification(
                review_content, project_name, commits, url_slug
            )
        
        # 如果没有用户邮箱，尝试从提交中获取
        if not user_email and commits and gitlab_token and gitlab_url and project_id:
            user_email = self.get_user_email_from_commits(
                commits, gitlab_token, gitlab_url, project_id
            )
        
        # 如果还是没有邮箱，使用兜底逻辑
        if not user_email:
            logger.warning("无法获取用户邮箱，使用Push兜底通知逻辑")
            return self._send_push_fallback_notification(
                review_content, project_name, commits, url_slug
            )
        
        # 从JARVIS获取群配置
        user_info, groups = self.get_jarvis_group_config(user_email)
        
        # 如果没有找到群配置，使用兜底逻辑
        if not groups:
            logger.info(f"用户 {user_email} 未配置JARVIS群，使用Push兜底通知逻辑")
            return self._send_push_fallback_notification(
                review_content, project_name, commits, url_slug
            )
        
        # 构建Push消息内容
        push_content = f"### 🚀 {project_name}: Push\n\n"
        push_content += "#### 提交记录:\n"
        
        for commit in commits:
            message = commit.get('message', '').strip()
            author = commit.get('author', 'Unknown Author')
            timestamp = commit.get('timestamp', '')
            url = commit.get('url', '#')
            push_content += (
                f"- **提交信息**: {message}\n"
                f"- **提交者**: {author}\n"
                f"- **时间**: {timestamp}\n"
                f"- [查看提交详情]({url})\n\n"
            )
        
        if review_content:
            push_content += f"#### AI Review 结果: \n {review_content}\n\n"
        
        # 发送到JARVIS配置的群
        success = False
        for group in groups:
            webhook_url = group.get('webhook_url')
            if webhook_url:
                # 获取需要艾特的用户列表
                at_users = []
                if user_info and user_info.get('feishu_id'):
                    at_users.append(user_info['feishu_id'])
                
                # 发送消息
                result = self.enhanced_feishu.send_simple_message(
                    webhook_url=webhook_url,
                    content=push_content,
                    title=f"🚀 {project_name} - Push事件",
                    at_users=at_users
                )
                
                if result:
                    success = True
                    logger.info(f"成功发送Push通知到JARVIS群: {group.get('name', 'Unknown')}")
                else:
                    logger.warning(f"发送Push通知到JARVIS群失败: {group.get('name', 'Unknown')}")
        
        # 如果所有JARVIS群都发送失败，使用兜底逻辑
        if not success:
            logger.warning("所有JARVIS群Push通知发送失败，使用兜底通知逻辑")
            return self._send_push_fallback_notification(
                review_content, project_name, commits, url_slug
            )
        
        return success
    
    def _send_push_fallback_notification(
        self,
        review_content: str,
        project_name: str,
        commits: List[Dict],
        url_slug: str = None
    ) -> bool:
        """
        Push事件的兜底通知逻辑
        """
        try:
            # 构建消息内容
            im_msg = f"### 🚀 {project_name}: Push\n\n"
            im_msg += "#### 提交记录:\n"
            
            for commit in commits:
                message = commit.get('message', '').strip()
                author = commit.get('author', 'Unknown Author')
                timestamp = commit.get('timestamp', '')
                url = commit.get('url', '#')
                im_msg += (
                    f"- **提交信息**: {message}\n"
                    f"- **提交者**: {author}\n"
                    f"- **时间**: {timestamp}\n"
                    f"- [查看提交详情]({url})\n\n"
                )
            
            if review_content:
                im_msg += f"#### AI Review 结果: \n {review_content}\n\n"
            
            # 使用原有的通知器发送
            notifier.send_notification(
                content=im_msg, 
                msg_type='markdown',
                title=f"{project_name} Push Event", 
                project_name=project_name,
                url_slug=url_slug
            )
            
            logger.info("Push兜底通知发送成功")
            return True
            
        except Exception as e:
            logger.error(f"Push兜底通知发送失败: {str(e)}")
            return False
