import json
import requests
import os
from typing import List, Optional, Dict, Any
from biz.utils.log import logger


class EnhancedFeishuNotifier:
    """增强的飞书通知器，支持艾特指定用户和动态webhook URL"""
    
    def __init__(self, webhook_url: str = None):
        """
        初始化增强飞书通知器
        :param webhook_url: 飞书机器人webhook地址
        """
        self.webhook_url = webhook_url
        self.enabled = os.environ.get('FEISHU_ENABLED', '0') == '1'

    def send_code_review_message(
        self,
        webhook_url: str,
        title: str,
        content: str,
        author_name: str = None,
        author_feishu_id: str = None,
        project_name: str = None,
        source_branch: str = None,
        target_branch: str = None,
        mr_url: str = None,
        at_users: List[str] = None
    ) -> bool:
        """
        发送代码审查消息到飞书群
        :param webhook_url: 飞书群机器人webhook地址
        :param title: 消息标题
        :param content: 审查内容
        :param author_name: 提交者姓名
        :param author_feishu_id: 提交者飞书ID
        :param project_name: 项目名称
        :param source_branch: 源分支
        :param target_branch: 目标分支
        :param mr_url: MR链接
        :param at_users: 需要艾特的用户飞书ID列表
        :return: 发送是否成功
        """
        if not webhook_url:
            logger.warning("飞书webhook URL为空，跳过发送")
            return False

        try:
            # 构建艾特用户的文本
            at_text = ""
            if at_users:
                at_mentions = []
                for user_id in at_users:
                    if user_id:
                        at_mentions.append(f"<at id={user_id}></at>")
                if at_mentions:
                    at_text = f"\n\n👥 相关人员: {' '.join(at_mentions)}"

            # 构建消息内容
            message_content = f"""**🔍 AI代码审查结果**

📋 **项目信息:**
• 项目名称: {project_name or 'Unknown'}
• 提交者: {author_name or 'Unknown'}
• 源分支: {source_branch or 'Unknown'}
• 目标分支: {target_branch or 'Unknown'}

🔗 **链接:** [查看详情]({mr_url or '#'})

📝 **审查结果:**
{content}

{at_text}"""

            data = {
                "msg_type": "interactive",
                "card": {
                    "schema": "2.0",
                    "config": {
                        "update_multi": True,
                        "style": {
                            "text_size": {
                                "normal_v2": {
                                    "default": "normal",
                                    "pc": "normal",
                                    "mobile": "heading"
                                }
                            }
                        }
                    },
                    "body": {
                        "direction": "vertical",
                        "padding": "12px 12px 12px 12px",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": message_content,
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px"
                            }
                        ]
                    },
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": title
                        },
                        "template": "blue",
                        "padding": "12px 12px 12px 12px"
                    }
                }
            }

            response = requests.post(
                url=webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code != 200:
                logger.error(f"飞书消息发送失败! webhook_url:{webhook_url}, error_msg:{response.text}")
                return False

            result = response.json()
            if result.get('msg') != "success":
                logger.error(f"发送飞书消息失败! webhook_url:{webhook_url}, errmsg:{result}")
                return False
            else:
                logger.info(f"飞书消息发送成功! webhook_url:{webhook_url}")
                return True

        except Exception as e:
            logger.error(f"飞书消息发送异常! webhook_url:{webhook_url}, error: {str(e)}")
            return False

    def send_simple_message(
        self,
        webhook_url: str,
        content: str,
        title: str = "通知",
        at_users: List[str] = None
    ) -> bool:
        """
        发送简单消息到飞书群
        :param webhook_url: 飞书群机器人webhook地址
        :param content: 消息内容
        :param title: 消息标题
        :param at_users: 需要艾特的用户飞书ID列表
        :return: 发送是否成功
        """
        if not webhook_url:
            logger.warning("飞书webhook URL为空，跳过发送")
            return False

        try:
            # 构建艾特用户的文本
            at_text = ""
            if at_users:
                at_mentions = []
                for user_id in at_users:
                    if user_id:
                        at_mentions.append(f"<at id={user_id}></at>")
                if at_mentions:
                    at_text = f"\n\n👥 相关人员: {' '.join(at_mentions)}"

            message_content = f"{content}{at_text}"

            data = {
                "msg_type": "interactive",
                "card": {
                    "schema": "2.0",
                    "config": {
                        "update_multi": True
                    },
                    "body": {
                        "direction": "vertical",
                        "padding": "12px 12px 12px 12px",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": message_content,
                                "text_align": "left",
                                "text_size": "normal_v2",
                                "margin": "0px 0px 0px 0px"
                            }
                        ]
                    },
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": title
                        },
                        "template": "blue",
                        "padding": "12px 12px 12px 12px"
                    }
                }
            }

            response = requests.post(
                url=webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code != 200:
                logger.error(f"飞书消息发送失败! webhook_url:{webhook_url}, error_msg:{response.text}")
                return False

            result = response.json()
            if result.get('msg') != "success":
                logger.error(f"发送飞书消息失败! webhook_url:{webhook_url}, errmsg:{result}")
                return False
            else:
                logger.info(f"飞书消息发送成功! webhook_url:{webhook_url}")
                return True

        except Exception as e:
            logger.error(f"飞书消息发送异常! webhook_url:{webhook_url}, error: {str(e)}")
            return False

    def send_text_message(self, webhook_url: str, text: str, at_users: List[str] = None) -> bool:
        """
        发送纯文本消息到飞书群
        :param webhook_url: 飞书群机器人webhook地址
        :param text: 文本内容
        :param at_users: 需要艾特的用户飞书ID列表
        :return: 发送是否成功
        """
        if not webhook_url:
            logger.warning("飞书webhook URL为空，跳过发送")
            return False

        try:
            # 构建艾特用户的文本
            at_text = ""
            if at_users:
                at_mentions = []
                for user_id in at_users:
                    if user_id:
                        at_mentions.append(f"<at user_id=\"{user_id}\"></at>")
                if at_mentions:
                    at_text = f" {' '.join(at_mentions)}"

            data = {
                "msg_type": "text",
                "content": {
                    "text": f"{text}{at_text}"
                }
            }

            response = requests.post(
                url=webhook_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code != 200:
                logger.error(f"飞书消息发送失败! webhook_url:{webhook_url}, error_msg:{response.text}")
                return False

            result = response.json()
            if result.get('msg') != "success":
                logger.error(f"发送飞书消息失败! webhook_url:{webhook_url}, errmsg:{result}")
                return False
            else:
                logger.info(f"飞书消息发送成功! webhook_url:{webhook_url}")
                return True

        except Exception as e:
            logger.error(f"飞书消息发送异常! webhook_url:{webhook_url}, error: {str(e)}")
            return False
