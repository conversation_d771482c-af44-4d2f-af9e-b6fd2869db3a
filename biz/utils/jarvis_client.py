import os
import requests
from typing import Optional, Dict, Any, List
from biz.utils.log import logger


class JarvisClient:
    """JARVIS服务客户端，用于调用JARVIS后端API"""
    
    def __init__(self):
        self.base_url = os.getenv('JARVIS_BASE_URL', 'http://localhost:8400')
        self.timeout = int(os.getenv('JARVIS_TIMEOUT', '10'))
        
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """发起HTTP请求的通用方法"""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"JARVIS API请求失败: {response.status_code}, {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"JARVIS API请求异常: {str(e)}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据邮箱获取用户信息"""
        if not email:
            return None
            
        logger.info(f"查询JARVIS用户信息，邮箱: {email}")
        
        result = self._make_request(
            'GET',
            '/user/email',
            params={'email': email}
        )
        
        if result:
            logger.info(f"成功获取用户信息: {result.get('name', 'Unknown')}")
        else:
            logger.info(f"未找到邮箱为 {email} 的用户")
            
        return result
    
    def get_groups_by_user_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据用户邮箱获取用户所在的AICR群配置"""
        if not email:
            return None
            
        logger.info(f"查询用户AICR群配置，邮箱: {email}")
        
        result = self._make_request(
            'GET',
            '/aicr/users/email/groups',
            params={'email': email}
        )
        
        if result and result.get('user'):
            groups_count = len(result.get('groups', []))
            logger.info(f"成功获取用户群配置，用户: {result['user']['name']}, 群数量: {groups_count}")
        else:
            logger.info(f"未找到邮箱为 {email} 的用户群配置")
            
        return result
    
    def get_groups_by_project_id(self, project_id: str) -> Optional[List[Dict[str, Any]]]:
        """根据项目ID获取对应的AICR群配置"""
        if not project_id:
            return None
            
        logger.info(f"查询项目AICR群配置，项目ID: {project_id}")
        
        result = self._make_request(
            'GET',
            f'/aicr/projects/{project_id}/groups'
        )
        
        if result:
            logger.info(f"成功获取项目群配置，群数量: {len(result)}")
        else:
            logger.info(f"未找到项目ID为 {project_id} 的群配置")
            
        return result


class GitlabUserClient:
    """GitLab用户信息客户端，用于获取提交人的详细信息"""
    
    def __init__(self, gitlab_url: str, gitlab_token: str):
        self.gitlab_url = gitlab_url.rstrip('/')
        self.gitlab_token = gitlab_token
        self.timeout = int(os.getenv('GITLAB_TIMEOUT', '10'))
    
    def get_commit_author_email(self, project_id: str, commit_sha: str) -> Optional[str]:
        """根据项目ID和提交SHA获取提交人邮箱"""
        if not project_id or not commit_sha:
            return None
            
        url = f"{self.gitlab_url}/api/v4/projects/{project_id}/repository/commits/{commit_sha}"
        headers = {
            'Private-Token': self.gitlab_token
        }
        
        try:
            logger.info(f"获取GitLab提交详情，项目: {project_id}, 提交: {commit_sha}")
            
            response = requests.get(url, headers=headers, timeout=self.timeout, verify=False)
            
            if response.status_code == 200:
                commit_data = response.json()
                author_email = commit_data.get('author_email')
                
                if author_email:
                    logger.info(f"成功获取提交人邮箱: {author_email}")
                else:
                    logger.warning(f"提交信息中未找到author_email字段")
                    
                return author_email
            else:
                logger.warning(f"获取GitLab提交详情失败: {response.status_code}, {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"GitLab API请求异常: {str(e)}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取用户详细信息"""
        if not username:
            return None
            
        url = f"{self.gitlab_url}/api/v4/users"
        headers = {
            'Private-Token': self.gitlab_token
        }
        params = {
            'username': username
        }
        
        try:
            logger.info(f"根据用户名查询GitLab用户信息: {username}")
            
            response = requests.get(url, headers=headers, params=params, timeout=self.timeout, verify=False)
            
            if response.status_code == 200:
                users = response.json()
                if users and len(users) > 0:
                    user = users[0]
                    email = user.get('email')
                    if email:
                        logger.info(f"成功获取用户邮箱: {email}")
                    else:
                        logger.warning(f"用户信息中未找到email字段")
                    return user
                else:
                    logger.warning(f"未找到用户名为 {username} 的用户")
                    return None
            else:
                logger.warning(f"查询GitLab用户信息失败: {response.status_code}, {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"GitLab API请求异常: {str(e)}")
            return None
