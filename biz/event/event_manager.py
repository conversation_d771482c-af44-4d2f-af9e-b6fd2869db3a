from blinker import Signal

from biz.entity.review_entity import MergeRequestReviewEntity, PushReviewEntity
from biz.service.review_service import ReviewService
from biz.utils.im import notifier
from biz.utils.jarvis_integration import JarvisIntegrationService

# 定义全局事件管理器（事件信号）
event_manager = {
    "merge_request_reviewed": Signal(),
    "push_reviewed": Signal(),
}


# 定义事件处理函数
def on_merge_request_reviewed(mr_review_entity: MergeRequestReviewEntity):
    # 初始化JARVIS集成服务
    jarvis_service = JarvisIntegrationService()

    # 尝试获取用户邮箱（从commits中获取）
    user_email = None
    if hasattr(mr_review_entity, 'gitlab_token') and hasattr(mr_review_entity, 'gitlab_url') and hasattr(mr_review_entity, 'project_id'):
        user_email = jarvis_service.get_user_email_from_commits(
            mr_review_entity.commits,
            mr_review_entity.gitlab_token,
            mr_review_entity.gitlab_url,
            mr_review_entity.project_id
        )

    # 发送增强通知
    success = jarvis_service.send_enhanced_notification(
        user_email=user_email,
        review_content=mr_review_entity.review_result,
        project_name=mr_review_entity.project_name,
        author_name=mr_review_entity.author,
        source_branch=mr_review_entity.source_branch,
        target_branch=mr_review_entity.target_branch,
        mr_url=mr_review_entity.url,
        commits=mr_review_entity.commits,
        gitlab_token=getattr(mr_review_entity, 'gitlab_token', None),
        gitlab_url=getattr(mr_review_entity, 'gitlab_url', None),
        project_id=getattr(mr_review_entity, 'project_id', None),
        url_slug=mr_review_entity.url_slug
    )

    if not success:
        # 如果增强通知失败，使用原有逻辑作为最后的兜底
        im_msg = f"""
### 🔀 {mr_review_entity.project_name}: Merge Request

#### 合并请求信息:
- **提交者:** {mr_review_entity.author}

- **源分支**: {mr_review_entity.source_branch}
- **目标分支**: {mr_review_entity.target_branch}
- **更新时间**: {mr_review_entity.updated_at}
- **提交信息:** {mr_review_entity.commit_messages}

- [查看合并详情]({mr_review_entity.url})

- **AI Review 结果:**

{mr_review_entity.review_result}
        """
        notifier.send_notification(content=im_msg, msg_type='markdown', title='Merge Request Review',
                                      project_name=mr_review_entity.project_name,
                                      url_slug=mr_review_entity.url_slug)

    # 记录到数据库
    ReviewService().insert_mr_review_log(mr_review_entity)


def on_push_reviewed(entity: PushReviewEntity):
    # 初始化JARVIS集成服务
    jarvis_service = JarvisIntegrationService()

    # 尝试获取用户邮箱（从commits中获取）
    user_email = None
    if hasattr(entity, 'gitlab_token') and hasattr(entity, 'gitlab_url') and hasattr(entity, 'project_id'):
        user_email = jarvis_service.get_user_email_from_commits(
            entity.commits,
            entity.gitlab_token,
            entity.gitlab_url,
            entity.project_id
        )

    # 发送Push增强通知
    success = jarvis_service.send_push_notification(
        user_email=user_email,
        review_content=entity.review_result,
        project_name=entity.project_name,
        commits=entity.commits,
        gitlab_token=getattr(entity, 'gitlab_token', None),
        gitlab_url=getattr(entity, 'gitlab_url', None),
        project_id=getattr(entity, 'project_id', None),
        url_slug=entity.url_slug
    )

    if not success:
        # 如果增强通知失败，使用原有逻辑作为最后的兜底
        im_msg = f"### 🚀 {entity.project_name}: Push\n\n"
        im_msg += "#### 提交记录:\n"

        for commit in entity.commits:
            message = commit.get('message', '').strip()
            author = commit.get('author', 'Unknown Author')
            timestamp = commit.get('timestamp', '')
            url = commit.get('url', '#')
            im_msg += (
                f"- **提交信息**: {message}\n"
                f"- **提交者**: {author}\n"
                f"- **时间**: {timestamp}\n"
                f"- [查看提交详情]({url})\n\n"
            )

        if entity.review_result:
            im_msg += f"#### AI Review 结果: \n {entity.review_result}\n\n"
        notifier.send_notification(content=im_msg, msg_type='markdown',
                                      title=f"{entity.project_name} Push Event", project_name=entity.project_name,
                                      url_slug=entity.url_slug)

    # 记录到数据库
    ReviewService().insert_push_review_log(entity)


# 连接事件处理函数到事件信号
event_manager["merge_request_reviewed"].connect(on_merge_request_reviewed)
event_manager["push_reviewed"].connect(on_push_reviewed)
