# JARVIS集成功能说明

## 概述

本功能实现了AI-CodeReview项目与JARVIS平台的集成，支持：

1. **智能用户识别**: 通过邮箱字段打通两个服务的人员关系
2. **定向飞书消息**: 根据JARVIS配置的飞书群发送代码审查结果
3. **艾特功能**: 支持艾特指定开发者
4. **兜底策略**: 如果JARVIS上没有匹配则保持现有发送逻辑

## 架构设计

```
AI-CodeReview → GitLab API → 获取用户邮箱 → JARVIS API → 获取群配置 → 发送飞书消息
     ↓                                                                    ↓
   兜底策略 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 配置说明

### 1. AI-CodeReview项目配置

在 `conf/.env` 文件中添加以下配置：

```bash
# JARVIS集成配置
JARVIS_INTEGRATION_ENABLED=1                    # 启用JARVIS集成 (1=启用, 0=禁用)
JARVIS_BASE_URL=http://localhost:8400          # JARVIS服务地址
JARVIS_TIMEOUT=10                              # JARVIS API请求超时时间(秒)
GITLAB_TIMEOUT=10                              # GitLab API请求超时时间(秒)

# 原有飞书配置(作为兜底策略)
FEISHU_ENABLED=1
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
```

### 2. JARVIS平台配置

确保JARVIS平台已经配置了：

1. **用户信息**: 用户的邮箱地址必须正确填写
2. **AICR群配置**: 创建飞书群配置，添加相关用户
3. **API接口**: 确保以下接口可用且无需token验证：
   - `GET /user/email?email={email}` - 根据邮箱获取用户信息
   - `GET /aicr/users/email/groups?email={email}` - 根据邮箱获取群配置

## 工作流程

### Merge Request事件流程

1. **接收Webhook**: AI-CodeReview接收GitLab的MR webhook
2. **代码审查**: 使用AI进行代码审查
3. **获取用户邮箱**: 
   - 从MR的commits中获取最新提交的SHA
   - 调用GitLab API获取提交详情中的author_email
   - 如果获取不到，尝试通过author_name查询用户信息
4. **查询JARVIS配置**:
   - 调用JARVIS API根据邮箱查询用户信息
   - 获取用户所在的飞书群配置
5. **发送消息**:
   - 如果找到群配置，发送到JARVIS配置的飞书群，并艾特相关用户
   - 如果没有找到，使用原有的兜底策略发送

### Push事件流程

类似MR事件，但针对Push事件进行优化。

## 新增文件说明

### 1. `biz/utils/jarvis_client.py`
- **JarvisClient**: JARVIS服务API客户端
- **GitlabUserClient**: GitLab用户信息API客户端

### 2. `biz/utils/im/enhanced_feishu.py`
- **EnhancedFeishuNotifier**: 增强的飞书通知器，支持艾特用户和动态webhook

### 3. `biz/utils/jarvis_integration.py`
- **JarvisIntegrationService**: 集成服务，协调各个组件

### 4. 修改的文件
- `biz/event/event_manager.py`: 集成JARVIS服务到事件处理
- `biz/entity/review_entity.py`: 添加GitLab相关信息字段
- `biz/queue/worker.py`: 传递GitLab信息到实体
- `conf/.env.dist`: 添加配置项

## API接口说明

### JARVIS后端新增接口

#### 1. 根据邮箱获取用户信息
```
GET /user/email?email={email}
```

响应示例：
```json
{
  "id": "user-uuid",
  "name": "张三",
  "feishu_id": "ou_xxx",
  "email": "<EMAIL>"
}
```

#### 2. 根据邮箱获取群配置
```
GET /aicr/users/email/groups?email={email}
```

响应示例：
```json
{
  "user": {
    "id": "user-uuid",
    "name": "张三",
    "feishu_id": "ou_xxx",
    "email": "<EMAIL>"
  },
  "groups": [
    {
      "id": "group-uuid",
      "name": "前端开发群",
      "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/xxx",
      "type": "normal"
    }
  ]
}
```

## 测试说明

运行测试脚本：
```bash
python test_jarvis_integration.py
```

测试内容包括：
1. JARVIS客户端API调用
2. GitLab客户端API调用
3. 增强飞书通知器
4. 集成服务完整流程

## 故障排查

### 1. JARVIS集成未生效
- 检查 `JARVIS_INTEGRATION_ENABLED` 是否设置为 `1`
- 检查 `JARVIS_BASE_URL` 是否正确
- 查看日志中是否有JARVIS API调用的错误信息

### 2. 无法获取用户邮箱
- 检查GitLab API权限是否足够
- 确认提交信息中包含author_email字段
- 检查用户在GitLab中是否设置了公开邮箱

### 3. 飞书消息发送失败
- 检查JARVIS中配置的webhook_url是否有效
- 确认飞书机器人权限设置正确
- 检查艾特的用户feishu_id是否正确

### 4. 兜底策略未生效
- 检查原有的飞书配置是否正确
- 确认 `FEISHU_ENABLED=1` 且 `FEISHU_WEBHOOK_URL` 有效

## 日志说明

关键日志信息：
- `查询JARVIS用户信息，邮箱: xxx` - 开始查询用户信息
- `成功获取用户群配置，用户: xxx, 群数量: x` - 成功获取群配置
- `成功发送到JARVIS群: xxx` - 消息发送成功
- `使用兜底通知逻辑` - 启用兜底策略

## 注意事项

1. **邮箱匹配**: 确保GitLab和JARVIS中的用户邮箱一致
2. **网络连通性**: 确保AI-CodeReview服务能够访问JARVIS服务
3. **API权限**: GitLab token需要有读取用户信息的权限
4. **飞书权限**: 飞书机器人需要有发送消息和艾特用户的权限
5. **性能考虑**: 增加了额外的API调用，可能会略微增加处理时间

## 版本兼容性

- 兼容现有的AI-CodeReview功能
- 向后兼容，禁用JARVIS集成时完全使用原有逻辑
- 支持渐进式部署，可以先在部分项目或用户中测试
